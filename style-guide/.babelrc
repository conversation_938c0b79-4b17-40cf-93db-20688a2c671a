{"presets": [["@babel/preset-env", {"modules": false}], "@babel/preset-react", "@babel/preset-typescript"], "plugins": [["@babel/plugin-transform-runtime", {"regenerator": true}], "babel-plugin-styled-components", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-export-default-from", ["@babel/plugin-proposal-decorators", {"decoratorsBeforeExport": true}]], "env": {"test": {"presets": [["@babel/preset-env", {"modules": "commonjs"}], "@babel/preset-react", "@babel/preset-typescript"]}}}