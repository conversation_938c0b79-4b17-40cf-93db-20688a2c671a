{"name": "paris2-styleguide", "repository": "*****************:fleetshipteam/paris2-web-base.git", "author": "", "license": "MIT", "scripts": {"start": "webpack-dev-server --mode=development --port 9002 --server-type https", "build": "webpack --mode=production && sass ./src/global.scss ./dist/paris2-styleguide.css", "analyze": "webpack --mode=production --env.analyze=true", "lint": "eslint src", "prettier": "prettier --write './**'", "test": "jest --coverage", "test:coverage": "jest --coverage", "watch-tests": "jest --watch", "coverage": "jest --coverage"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/eslint-parser": "^7.22.0", "@babel/plugin-proposal-class-properties": "^7.10.1", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/plugin-proposal-object-rest-spread": "7.9.0", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/preset-env": "^7.9.0", "@babel/preset-flow": "^7.27.1", "@babel/preset-react": "^7.9.1", "@babel/preset-typescript": "^7.27.1", "@babel/runtime": "^7.9.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^14.3.1", "@types/jest": "^29.5.14", "babel-eslint": "^10.0.3", "babel-jest": "^29.7.0", "babel-loader": "^8.0.6", "babel-plugin-styled-components": "^1.10.7", "bootstrap": "^4.5.2", "css-loader": "^3.3.2", "eslint": "^8.57.0", "eslint-config-prettier": "^6.7.0", "eslint-config-react-important-stuff": "^2.0.0", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-react": "^7.32.2", "file-loader": "^6.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^30.0.5", "prettier": "^1.19.1", "pretty-quick": "^2.0.1", "react-hook-form": "^7.21.0", "sass": "^1.26.3", "sass-loader": "^8.0.2", "single-spa-react": "^6.0.1", "source-map-loader": "^1.1.0", "stylelint": "^13.2.1", "stylelint-scss": "^3.16.0", "systemjs-webpack-interop": "^1.1.2", "ts-loader": "^9.4.2", "typescript": "^4.9.5", "webpack": "^5.82.0", "webpack-cli": "^5.0.2", "webpack-config-single-spa-react": "^4.0.4", "webpack-dev-server": "^4.13.3", "webpack-merge": "^5.8.0", "yup": "^0.32.11"}, "dependencies": {"@babel/plugin-proposal-export-default-from": "^7.10.1", "@tanstack/react-table": "^8.21.3", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "d3": "^7.9.0", "highcharts": "^12.3.0", "highcharts-react-official": "^3.2.2", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-bootstrap": "^1.6.0", "react-bootstrap-typeahead": "^6.0.0-alpha.4", "react-dom": "^18.2.0", "react-icomoon": "^2.5.7", "react-router-dom": "^5.3.4", "style-loader": "^1.2.1", "ts-jest": "^29.4.1"}, "engines": {"node": ">=22"}, "engineStrict": true}