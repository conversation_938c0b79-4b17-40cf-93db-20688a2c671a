import { renderHook, act } from '@testing-library/react';
import { useRef } from 'react';
import useResizeObserver from '../DashboardWidget/hooks/useResizeObserver';

let callback: any;

class MockResizeObserver {
  constructor(cb: any) {
    // We store the callback function passed to the constructor.
    callback = cb;
  }
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Jest will replace the global ResizeObserver with our mock.
beforeAll(() => {
  // @ts-ignore
  global.ResizeObserver = MockResizeObserver;
});

// A helper function to trigger a "resize" event.
const triggerResize = (width: number) => {
  act(() => {
    // We call the stored callback with a mock entry.
    callback([{ contentRect: { width } }]);
  });
};

describe('useResizeObserver', () => {
  it('should initialize with a width of 0', () => {

    const { result } = renderHook(() => {
      const ref = useRef(null);
      const width = useResizeObserver(ref);
      return { width };
    });

    expect(result.current.width).toBe(0);
  });

  it('should update the width when the element resizes', () => {
    const { result } = renderHook(() => {
      // We create a mock ref object that contains a 'current' property.
      const ref = { current: document.createElement('div') };
      return useResizeObserver(ref);
    });

    // Simulate a resize to a width of 100px.
    triggerResize(100);
    expect(result.current).toBe(100);

    // Simulate another resize to a width of 250px.
    triggerResize(250);
    expect(result.current).toBe(250);
  });

  it('should not update the width if the new width is the same', () => {
    const { result } = renderHook(() => {
      const ref = { current: document.createElement('div') };
      return useResizeObserver(ref);
    });

    // Initial resize to 100px.
    triggerResize(100);
    expect(result.current).toBe(100);

    triggerResize(100);
    expect(result.current).toBe(100);
  });

  it('should return 0 if the ref is null', () => {
    const { result } = renderHook(() => {
      const ref = { current: null };
      return useResizeObserver(ref);
    });

    expect(result.current).toBe(0);
  });
});