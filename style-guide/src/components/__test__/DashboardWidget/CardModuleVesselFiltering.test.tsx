import React from "react";
import { render, screen } from "@testing-library/react";
import CardModule from "../../DashboardWidget/iCard/CardModule";
import { WidgetConstant } from "../../DashboardWidget/types/widget.constant";
import { Vessel, MultiSelectConfig } from "../../DashboardWidget/types/card-types";

// Mock the hooks and components
jest.mock("../../DashboardWidget/hooks/useMediaQuery", () => ({
  useMediaQuery: () => false,
}));

jest.mock("../../DashboardWidget/iCard/CardList", () => {
  return function MockCardList({ vessels }: { vessels: Vessel[] }) {
    return (
      <div data-testid="card-list">
        {vessels.length === 0 ? (
          <div data-testid="no-data">No Data Available</div>
        ) : (
          vessels.map((vessel, index) => (
            <div key={index} data-testid={`vessel-${vessel.name}`}>
              {vessel.name}
            </div>
          ))
        )}
      </div>
    );
  };
});

jest.mock("../../DashboardWidget/iCard/CardGrid", () => {
  return function MockCardGrid({ vessels }: { vessels: Vessel[] }) {
    return (
      <div data-testid="card-grid">
        {vessels.length === 0 ? (
          <div data-testid="no-data">No Data Available</div>
        ) : (
          vessels.map((vessel, index) => (
            <div key={index} data-testid={`vessel-${vessel.name}`}>
              {vessel.name}
            </div>
          ))
        )}
      </div>
    );
  };
});

describe("CardModule - Vessel Filtering Logic", () => {
  const mockVessels: Vessel[] = [
    {
      name: "Vessel A",
      vessel_id: 1,
      vessel_code: "VA001",
      type: "Active",
      vesselData: [],
      ra_level: "ROUTINE",
    },
    {
      name: "Vessel B",
      vessel_id: 2,
      vessel_code: "VB002",
      type: "Active",
      vesselData: [],
      ra_level: "SPECIAL",
    },
    {
      name: "Vessel C",
      vessel_id: 3,
      vessel_code: "VC003",
      type: "Active", // Changed to Active so it shows up in default tab
      vesselData: [],
      ra_level: "CRITICAL",
    },
  ];

  const mockMultiVesselSelects: MultiSelectConfig[] = [
    {
      placeholder: "Select Vessels",
      width: "300px",
      groups: [
        {
          id: 1,
          title: "Group 1",
          vessels: [
            { vessel_id: 1, name: "Vessel A", vessel_account_code_new: "VA001" },
            { vessel_id: 2, name: "Vessel B", vessel_account_code_new: "VB002" },
            { vessel_id: 3, name: "Vessel C", vessel_account_code_new: "VC003" },
          ],
        },
      ],
    },
  ];

  const baseProps = {
    title: "Test Widget",
    vessels: mockVessels,
    multiVesselSelects: mockMultiVesselSelects,
    staticData: {
      tabs: ["Active", "Inactive"],
      badgeColors: ["#ff0000", "#00ff00"],
      barChartMaxRange: 100,
    },
    visibleConfig: {
      IsLastUpdatedVisible: false,
      IsRefereshIconVisible: false,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsAllTabVisible: true, // Enable "All" tab so all vessels show
      filterApplyonRenderData: "vessel_id",
    },
    componentView: {
      defaultComponent: "list" as const,
    },
    sizeKey: "md" as const,
    onRefresh: jest.fn(),
    onSendEmail: jest.fn(),
    onVesselClick: jest.fn(),
    fetchNextPage: jest.fn(),
    onChangeActiveTab: jest.fn(),
    columns: [],
    pagination: {},
    responsive: false,
    responsiveConfig: {
      component: () => <div>Responsive Component</div>,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows no data when no vessels are selected for non-RISK_ASSESSMENT widgets", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: WidgetConstant.DEFICIENCIES, // Not RISK_ASSESSMENT
      },
    };

    render(<CardModule {...props} />);

    // Should show no data since no vessels are selected
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel A")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel B")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel C")).not.toBeInTheDocument();
  });

  it("shows all vessels when no vessels are selected for RISK_ASSESSMENT widget", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: WidgetConstant.RISK_ASSESSMENT, // RISK_ASSESSMENT widget
      },
    };

    render(<CardModule {...props} />);

    // Should show all vessels since it's RISK_ASSESSMENT widget
    expect(screen.queryByTestId("no-data")).not.toBeInTheDocument();
    expect(screen.getByTestId("vessel-Vessel A")).toBeInTheDocument();
    expect(screen.getByTestId("vessel-Vessel B")).toBeInTheDocument();
    expect(screen.getByTestId("vessel-Vessel C")).toBeInTheDocument();
  });

  it("shows no data when no vessels are selected for SURVEYS_CERTIFICATES widget", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: WidgetConstant.SURVEYS_CERTIFICATES, // Not RISK_ASSESSMENT
      },
    };

    render(<CardModule {...props} />);

    // Should show no data since no vessels are selected
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel A")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel B")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel C")).not.toBeInTheDocument();
  });

  it("shows no data when no vessels are selected for ITINERARY_ETA widget", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: WidgetConstant.ITINERARY_ETA, // Not RISK_ASSESSMENT
      },
    };

    render(<CardModule {...props} />);

    // Should show no data since no vessels are selected
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel A")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel B")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel C")).not.toBeInTheDocument();
  });

  it("shows no data when no vessels are selected for OWNER_FINANCIAL_REPORTING widget", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: WidgetConstant.OWNER_FINANCIAL_REPORTING, // Not RISK_ASSESSMENT
      },
    };

    render(<CardModule {...props} />);

    // Should show no data since no vessels are selected
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel A")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel B")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel C")).not.toBeInTheDocument();
  });

  it("shows no data when no vessels are selected and no configKey is provided", () => {
    const props = {
      ...baseProps,
      staticData: {
        ...baseProps.staticData,
        configKey: undefined, // No configKey
      },
    };

    render(<CardModule {...props} />);

    // Should show no data since no vessels are selected and it's not RISK_ASSESSMENT
    expect(screen.getByTestId("no-data")).toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel A")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel B")).not.toBeInTheDocument();
    expect(screen.queryByTestId("vessel-Vessel C")).not.toBeInTheDocument();
  });
});
