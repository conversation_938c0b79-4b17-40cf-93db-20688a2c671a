import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { ColumnDef } from "@tanstack/react-table";
import InfiniteScrollTable from "../../DashboardWidget/iCard/InfiniteScrollTable";
interface TestData {
  id: number;
  name: string;
  status: string;
  value: number;
  longText?: string;
}

const mockData: TestData[] = [
  { id: 1, name: "Item 1", status: "Active", value: 100 },
  { id: 2, name: "Item 2", status: "Inactive", value: 200 },
  { id: 3, name: "Item 3", status: "Active", value: 300 },
];

const mockDataWithLongText: TestData[] = [
  { id: 1, name: "Item 1", status: "Active", value: 100, longText: "This is a very long text that should be truncated and show a tooltip when hovered over in the table cell" },
  { id: 2, name: "Item 2", status: "Inactive", value: 200, longText: "Another very long text that exceeds the maximum length limit" },
];

const mockColumns: ColumnDef<TestData>[] = [
  {
    id: "name",
    accessorKey: "name",
    header: "Name",
    cell: ({ getValue }) => <span>{getValue() as string}</span>,
  },
  {
    id: "status",
    accessorKey: "status",
    header: "Status",
    cell: ({ getValue }) => <span>{getValue() as string}</span>,
  },
  {
    id: "value",
    accessorKey: "value",
    header: "Value",
    cell: ({ getValue }) => <span>{getValue() as number}</span>,
  },
];

const mockPagination = {
  totalItems: 10,
  totalPages: 5,
  page: 1,
  pageSize: 3,
};

const defaultProps = {
  data: mockData,
  columns: mockColumns,
  isLoading: false,
  isFetchingNextPage: false,
  pagination: mockPagination,
  sorting: {
    sorting: [],
    onSortingChange: jest.fn(),
  },
  fetchNextPage: jest.fn(),
};

describe("InfiniteScrollTable Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Rendering", () => {
    test("should render table with data", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
      expect(screen.getByText("Item 1")).toBeInTheDocument();
      expect(screen.getByText("Item 2")).toBeInTheDocument();
      expect(screen.getByText("Item 3")).toBeInTheDocument();
      expect(screen.getAllByText("Active").length).toBeGreaterThan(0);
      expect(screen.getByText("Inactive")).toBeInTheDocument();
    });

    test("should render table headers correctly", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      expect(screen.getByText("Name")).toBeInTheDocument();
      expect(screen.getByText("Status")).toBeInTheDocument();
      expect(screen.getByText("Value")).toBeInTheDocument();
    });
  });

  describe("Loading States", () => {
    test("should show skeleton rows when loading", () => {
      const loadingProps = { ...defaultProps, isLoading: true, data: [] };
      render(<InfiniteScrollTable {...loadingProps} />);
      const skeletonCells = screen.getAllByText("", {
        selector: ".skeleton-cell",
      });
      expect(skeletonCells.length).toBeGreaterThan(0);
    });

    test("should show loading indicator when fetching next page", () => {
      const fetchingProps = { ...defaultProps, isFetchingNextPage: true };
      render(<InfiniteScrollTable {...fetchingProps} />);
      expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    test("should not show loading indicator when not fetching", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      expect(screen.queryByText("Loading more...")).not.toBeInTheDocument();
    });
  });

 describe("Row Interactions", () => {
    test("should call onRowClick when clicking vessel cell", () => {
      const mockOnRowClick = jest.fn();
      const clickableProps = { ...defaultProps, onRowClick: mockOnRowClick };
      const columnsWithVessel = [
        { ...mockColumns[0], id: "vessel", accessorKey: "name" },
        ...mockColumns.slice(1),
      ] as ColumnDef<TestData>[];
      render(
        <InfiniteScrollTable {...clickableProps} columns={columnsWithVessel} />
      );
      const nameCell = screen.getByText("Item 1");
      fireEvent.click(nameCell);
      expect(mockOnRowClick).toHaveBeenCalledWith(
        mockData[0],
        undefined,
        undefined
      );
    });

    test("should not call onRowClick when not provided", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      const firstRow = screen.getByText("Item 1").closest("tr");
      expect(() => fireEvent.click(firstRow!)).not.toThrow();
    });

    test("should handle row selection (no crash)", () => {
      const selectionProps = {
        ...defaultProps,
        rowSelection: { "0": true, "2": true },
      } as any;
      expect(() =>
        render(<InfiniteScrollTable {...selectionProps} />)
      ).not.toThrow();
    });
  });


describe("Infinite Scroll Integration", () => {
    test("should call fetchNextPage through useInfiniteScroll hook", async () => {
      const mockFetchNextPage = jest.fn();
      const scrollProps = { ...defaultProps, fetchNextPage: mockFetchNextPage };
      expect(() =>
        render(<InfiniteScrollTable {...scrollProps} />)
      ).not.toThrow();
    });

    test("should compute next page availability without crashing", () => {
      expect(() =>
        render(<InfiniteScrollTable {...defaultProps} />)
      ).not.toThrow();
      const lastPageProps = {
        ...defaultProps,
        pagination: { ...mockPagination, page: 5, totalPages: 5 },
      };
      expect(() =>
        render(<InfiniteScrollTable {...lastPageProps} />)
      ).not.toThrow();
    });
  });

  describe("Expanded State", () => {
    test("should handle expanded state for sub-rows", () => {
      const expandableProps = { ...defaultProps, subRowKey: "subItems" };
      render(<InfiniteScrollTable {...expandableProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
    });
  });

 describe("Edge Cases", () => {
    test("should handle empty columns array", () => {
      const noColumnsProps = { ...defaultProps, columns: [] };
      render(<InfiniteScrollTable {...noColumnsProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
    });

    test("should handle null data gracefully (no crash)", () => {
      const nullDataProps = { ...defaultProps, data: [] as any };
      expect(() =>
        render(<InfiniteScrollTable {...nullDataProps} />)
      ).not.toThrow();
    });

    test("should handle very large datasets", () => {
      const largeData = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        name: `Item ${i}`,
        status: i % 2 === 0 ? "Active" : "Inactive",
        value: i * 10,
      }));
      const largeDataProps = { ...defaultProps, data: largeData };
      render(<InfiniteScrollTable {...largeDataProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
      expect(screen.getByText("Item 0")).toBeInTheDocument();
    });
  });


  describe("Accessibility", () => {
    test("should have proper table structure", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
      expect(screen.getAllByRole("columnheader")).toHaveLength(3);
      expect(screen.getAllByRole("row")).toHaveLength(4);
    });

    test("should render column headers", () => {
      render(<InfiniteScrollTable {...defaultProps} />);
      const headers = screen.getAllByRole("columnheader");
      expect(headers.length).toBeGreaterThan(0);
    });
  });

  describe("Performance", () => {
    test("should memoize data correctly", () => {
      const { rerender } = render(<InfiniteScrollTable {...defaultProps} />);
      rerender(<InfiniteScrollTable {...defaultProps} />);
      expect(screen.getByRole("table")).toBeInTheDocument();
    });

    test("should handle rapid sorting changes", () => {
      const mockOnSortingChange = jest.fn();
      const sortingProps = {
        ...defaultProps,
        sorting: {
          sorting: [],
          onSortingChange: mockOnSortingChange,
        },
      };
      render(<InfiniteScrollTable {...sortingProps} />);
      const nameHeader = screen.getByText("Name");
      fireEvent.click(nameHeader);
      fireEvent.click(nameHeader);
      fireEvent.click(nameHeader);
      expect(mockOnSortingChange).toHaveBeenCalledTimes(3);
    });
  });

  describe("Tooltip functionality", () => {
    const mockColumnsWithLongText: ColumnDef<TestData>[] = [
      {
        id: "vessel", // This ID triggers maxLength = 15 in the component
        accessorKey: "longText",
        header: "Vessel",
        cell: ({ getValue }) => <span>{getValue() as string}</span>,
      },
      {
        id: "task_required", // This ID triggers maxLength = 50 in the component
        accessorKey: "longText",
        header: "Task Required",
        cell: ({ getValue }) => <span>{getValue() as string}</span>,
      },
    ];

    const mockProps = {
      data: mockDataWithLongText,
      columns: mockColumnsWithLongText as ColumnDef<object>[],
      isLoading: false,
      pagination: { totalItems: 2, totalPages: 1, page: 1, pageSize: 10 },
      sorting: { sorting: [], onSortingChange: jest.fn() },
      fetchNextPage: jest.fn(),
    };

    test("renders tooltip for truncated text with proper z-index", () => {
      const { container } = render(<InfiniteScrollTable {...mockProps} />);

      // Check if OverlayTrigger is rendered for long text (vessel column should be truncated)
      const overlayTrigger = container.querySelector('.tooltip');
      const truncatedText = container.querySelector('span[style*="cursor: pointer"]');

      // Either the tooltip or the truncated text should be present
      expect(truncatedText || overlayTrigger).toBeTruthy();
    });

    test("applies correct CSS classes for modal tooltip z-index", () => {
      // This test verifies that the CSS classes are properly applied
      // The actual z-index behavior would be tested in integration tests
      const { container } = render(
        <div className="ra-modal-content">
          <InfiniteScrollTable {...mockProps} />
        </div>
      );

      const modalContent = container.querySelector('.ra-modal-content');
      expect(modalContent).toBeInTheDocument();

      // Verify that the modal content container exists
      // The CSS rules for tooltip z-index are applied via SCSS
      expect(modalContent).toHaveClass('ra-modal-content');
    });
  });
});
