import React from "react";
import { render, fireEvent, screen, waitFor } from "@testing-library/react";
import { CardDropdown } from "../../DashboardWidget/iCard/CardDropdown";
import { VesselGroup } from "../../DashboardWidget/types/card-types";

describe("CardDropdown - Filtered Select All Functionality", () => {
  const mockGroups: VesselGroup[] = [
    {
      id: 1,
      title: "Group 1",
      vessels: [
        { vessel_id: 1, name: "Alpha Vessel", vessel_account_code_new: "V001" },
        { vessel_id: 2, name: "Beta Vessel", vessel_account_code_new: "V002" },
        { vessel_id: 3, name: "Gamma Ship", vessel_account_code_new: "V003" },
      ],
    },
    {
      id: 2,
      title: "Group 2",
      vessels: [
        { vessel_id: 4, name: "Delta Vessel", vessel_account_code_new: "V004" },
        { vessel_id: 5, name: "Echo Ship", vessel_account_code_new: "V005" },
      ],
    },
  ];

  const mockProps = {
    groups: mockGroups,
    selectedItems: [],
    onSelectionChange: jest.fn(),
    placeholder: "Select Vessels",
    isSearchBoxVisible: true,
    isSelectAllVisible: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("selects only filtered vessels when search is applied", async () => {
    render(<CardDropdown {...mockProps} />);
    
    // Open dropdown
    const dropdownButton = screen.getByRole("button");
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
    });
    
    // Search for "Vessel" - should show Alpha Vessel, Beta Vessel, Delta Vessel
    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Vessel" } });
    
    // Verify filtered results are shown
    expect(screen.getByText("Alpha Vessel")).toBeInTheDocument();
    expect(screen.getByText("Beta Vessel")).toBeInTheDocument();
    expect(screen.getByText("Delta Vessel")).toBeInTheDocument();
    expect(screen.queryByText("Gamma Ship")).not.toBeInTheDocument();
    expect(screen.queryByText("Echo Ship")).not.toBeInTheDocument();
    
    // Click Select All
    fireEvent.click(screen.getByText("Select All"));
    
    // Should only select the filtered vessels
    expect(mockProps.onSelectionChange).toHaveBeenCalledWith([
      "Alpha Vessel",
      "Beta Vessel",
      "Delta Vessel"
    ]);
  });

  it("maintains existing selections when selecting filtered vessels", async () => {
    const propsWithExistingSelection = {
      ...mockProps,
      selectedItems: ["Echo Ship"], // Pre-selected vessel not in search results
    };
    
    render(<CardDropdown {...propsWithExistingSelection} />);
    
    // Open dropdown
    const dropdownButton = screen.getByRole("button");
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
    });
    
    // Search for "Vessel"
    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Vessel" } });
    
    // Click Select All
    fireEvent.click(screen.getByText("Select All"));
    
    // Should add filtered vessels to existing selection
    expect(propsWithExistingSelection.onSelectionChange).toHaveBeenCalledWith([
      "Echo Ship",
      "Alpha Vessel",
      "Beta Vessel",
      "Delta Vessel"
    ]);
  });

  it("shows correct button text based on filtered selection state", async () => {
    const propsWithPartialSelection = {
      ...mockProps,
      selectedItems: ["Alpha Vessel", "Gamma Ship"],
    };
    
    render(<CardDropdown {...propsWithPartialSelection} />);
    
    // Open dropdown
    const dropdownButton = screen.getByRole("button");
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
    });
    
    // Search for "Alpha" - only Alpha Vessel is shown and it's selected
    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Alpha" } });
    
    // Should show "Unselect All" since all filtered vessels are selected
    expect(screen.getByText("Unselect All")).toBeInTheDocument();
    
    // Search for "Vessel" - Alpha Vessel is selected, Beta and Delta are not
    fireEvent.change(searchInput, { target: { value: "Vessel" } });
    
    // Should show "Select All" since not all filtered vessels are selected
    expect(screen.getByText("Select All")).toBeInTheDocument();
  });

  it("unselects only filtered vessels when clicking Unselect All", async () => {
    const propsWithSelection = {
      ...mockProps,
      selectedItems: ["Alpha Vessel", "Beta Vessel", "Gamma Ship", "Delta Vessel", "Echo Ship"],
    };
    
    render(<CardDropdown {...propsWithSelection} />);
    
    // Open dropdown
    const dropdownButton = screen.getByRole("button");
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
    });
    
    // Search for "Ship" - should show Gamma Ship, Echo Ship
    const searchInput = screen.getByPlaceholderText("Search");
    fireEvent.change(searchInput, { target: { value: "Ship" } });
    
    // Should show "Unselect All" since all filtered vessels are selected
    expect(screen.getByText("Unselect All")).toBeInTheDocument();
    
    // Click Unselect All
    fireEvent.click(screen.getByText("Unselect All"));
    
    // Should unselect only the filtered vessels (Gamma Ship, Echo Ship)
    expect(propsWithSelection.onSelectionChange).toHaveBeenCalledWith([
      "Alpha Vessel",
      "Beta Vessel",
      "Delta Vessel"
    ]);
  });

  it("works correctly when no search filter is applied", async () => {
    render(<CardDropdown {...mockProps} />);
    
    // Open dropdown
    const dropdownButton = screen.getByRole("button");
    fireEvent.click(dropdownButton);
    
    // Wait for dropdown to open
    await waitFor(() => {
      expect(screen.getByPlaceholderText("Search")).toBeInTheDocument();
    });
    
    // Don't apply any search filter, just click Select All
    fireEvent.click(screen.getByText("Select All"));
    
    // Should select all vessels
    expect(mockProps.onSelectionChange).toHaveBeenCalledWith([
      "Alpha Vessel",
      "Beta Vessel",
      "Gamma Ship",
      "Delta Vessel",
      "Echo Ship"
    ]);
  });
});
