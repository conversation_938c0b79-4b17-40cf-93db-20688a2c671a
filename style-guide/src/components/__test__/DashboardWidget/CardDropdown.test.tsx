import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { CardDropdown } from "../../DashboardWidget/iCard/CardDropdown";
import { VesselGroup } from "../../DashboardWidget/types/card-types";

// Mock CardDropdownMenu component
jest.mock("../../DashboardWidget/iCard/CardDropdownMenu", () => ({
  CardDropdownMenu: () => <div data-testid="dropdown-menu">Menu Content</div>,
}));

const mockToggle = jest.fn();
let mockIsOpen = false;

// Mock useDropdown hook
jest.mock("../../DashboardWidget/hooks/useDropdown", () => ({
  useDropdown: () => ({
    dropdownRef: { current: null },
    isOpen: mockIsOpen,
    toggleDropdown: mockToggle,
  }),
}));

const mockGroups: VesselGroup[] = [
  {
    id: 1,
    title: "Group 1",
    vessels: [
      {
        name: "Vessel 1",
        vessel_id: 1,
        vessel_account_code_new: "VAC1",
      },
      {
        name: "Vessel 2",
        vessel_id: 2,
        vessel_account_code_new: "VAC2",
      },
    ],
  },
  {
    id: 2,
    title: "Group 2",
    vessels: [
      {
        name: "Vessel 3",
        vessel_id: 3,
        vessel_account_code_new: "VAC3",
      },
      {
        name: "Vessel 4",
        vessel_id: 4,
        vessel_account_code_new: "VAC4",
      },
    ],
  },
];

describe("CardDropdown", () => {
  const defaultProps = {
    groups: mockGroups,
    selectedItems: [] as string[],
    onSelectionChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockIsOpen = false;
  });

  it("renders with default placeholder", () => {
    render(<CardDropdown {...defaultProps} />);
    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("renders with custom placeholder", () => {
    render(<CardDropdown {...defaultProps} placeholder="Select Vessels" />);
    expect(screen.getByText("Select Vessels")).toBeInTheDocument();
  });

  it("displays selected vessels correctly", () => {
    const props = {
      ...defaultProps,
      selectedItems: ["Vessel 1", "Vessel 2"],
    };
    render(<CardDropdown {...props} />);
    expect(screen.getByText("Vessel 1, Vessel 2")).toBeInTheDocument();
  });

  it("toggles dropdown when clicked", () => {
    render(<CardDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole("button");
    fireEvent.click(dropdownHeader);

    expect(mockToggle).toHaveBeenCalled();
  });

  it("shows dropdown menu when open", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("hides dropdown menu when closed", () => {
    mockIsOpen = false;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.queryByTestId("dropdown-menu")).not.toBeInTheDocument();
  });

  it("handles selection change callback", () => {
    const onSelectionChange = jest.fn();
    mockIsOpen = true;

    render(
      <CardDropdown {...defaultProps} onSelectionChange={onSelectionChange} />
    );

    const dropdownHeader = screen.getByRole("button");
    fireEvent.click(dropdownHeader);

    expect(mockToggle).toHaveBeenCalled();
  });

  it("applies chevron rotation when open", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    const chevron = screen.getByLabelText("toggle-chevron");
    expect(chevron).toHaveClass("raChevron", "raRotate");
  });

  // Additional comprehensive test cases
  it("handles empty groups array", () => {
    const emptyGroupsProps = { ...defaultProps, groups: [] };
    render(<CardDropdown {...emptyGroupsProps} />);

    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("displays truncated vessel names with +more badge", () => {
    const manySelectedProps = {
      ...defaultProps,
      selectedItems: [
        "Vessel 1",
        "Vessel 2",
        "Vessel 3",
        "Vessel 4",
        "Vessel 5",
      ],
      width: "200px",
    };

    render(<CardDropdown {...manySelectedProps} />);
    // With dynamic calculation, the exact number may vary, so we check for any "+X more" pattern
    expect(screen.getByText(/\+\d+ more/)).toBeInTheDocument();
  });

  it("displays all vessel names when count is within limit", () => {
    const fewSelectedProps = {
      ...defaultProps,
      selectedItems: ["Vessel 1", "Vessel 2"],
    };

    render(<CardDropdown {...fewSelectedProps} />);
    expect(screen.getByText("Vessel 1, Vessel 2")).toBeInTheDocument();
    expect(screen.queryByText(/\+\d+ more/)).not.toBeInTheDocument();
  });

  it("handles different width configurations", () => {
    const wideProps = {
      ...defaultProps,
      selectedItems: ["V1", "V2", "V3", "V4", "V5", "V6", "V7", "V8", "V9"],
      width: "300px",
    };

    render(<CardDropdown {...wideProps} />);
    // With dynamic calculation, the exact number may vary, so we check for any "+X more" pattern
    expect(screen.getByText(/\+\d+ more/)).toBeInTheDocument();
  });

  it("renders with search box visible by default", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("renders without search box when disabled", () => {
    const noSearchProps = { ...defaultProps, isSearchBoxVisible: false };
    mockIsOpen = true;

    render(<CardDropdown {...noSearchProps} />);
    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("renders with select all visible by default", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("renders without select all when disabled", () => {
    const noSelectAllProps = { ...defaultProps, isSelectAllVisible: false };
    mockIsOpen = true;

    render(<CardDropdown {...noSelectAllProps} />);
    expect(screen.getByTestId("dropdown-menu")).toBeInTheDocument();
  });

  it("has correct accessibility attributes", () => {
    render(<CardDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole("button");
    expect(dropdownHeader).toHaveAttribute("aria-label", "My Vessels");
    expect(dropdownHeader).toHaveAttribute("aria-haspopup", "listbox");
    expect(dropdownHeader).toHaveAttribute("aria-expanded", "false");
  });

  it("updates accessibility attributes when open", () => {
    mockIsOpen = true;
    render(<CardDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole("button");
    expect(dropdownHeader).toHaveAttribute("aria-expanded", "true");
  });

  it("handles single vessel selection", () => {
    const singleSelectedProps = {
      ...defaultProps,
      selectedItems: ["Vessel 1"],
    };

    render(<CardDropdown {...singleSelectedProps} />);
    expect(screen.getByText("Vessel 1")).toBeInTheDocument();
  });

  it("handles groups with no vessels", () => {
    const emptyVesselsGroups = [
      {
        id: 1,
        title: "Empty Group",
        vessels: [],
      },
    ];

    const emptyVesselsProps = { ...defaultProps, groups: emptyVesselsGroups };
    render(<CardDropdown {...emptyVesselsProps} />);

    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("handles groups with mixed vessel counts", () => {
    const mixedGroups = [
      ...mockGroups,
      {
        id: 3,
        title: "Single Vessel Group",
        vessels: [
          {
            name: "Solo Vessel",
            vessel_id: 5,
            vessel_account_code_new: "SOLO",
          },
        ],
      },
    ];

    const mixedProps = { ...defaultProps, groups: mixedGroups };
    render(<CardDropdown {...mixedProps} />);

    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("handles very long vessel names", () => {
    const longNameProps = {
      ...defaultProps,
      selectedItems: ["Very Long Vessel Name That Exceeds Normal Length"],
    };

    render(<CardDropdown {...longNameProps} />);
    expect(
      screen.getByText("Very Long Vessel Name That Exceeds Normal Length")
    ).toBeInTheDocument();
  });

  it("handles special characters in vessel names", () => {
    const specialCharProps = {
      ...defaultProps,
      selectedItems: ["Vessel-1", "Vessel_2", "Vessel@3"],
    };

    render(<CardDropdown {...specialCharProps} />);
    expect(
      screen.getByText("Vessel-1, Vessel_2, Vessel@3")
    ).toBeInTheDocument();
  });

  it("maintains selection state across re-renders", () => {
    const { rerender } = render(<CardDropdown {...defaultProps} />);

    // Re-render with same props
    rerender(<CardDropdown {...defaultProps} />);

    expect(screen.getByText("My Vessels")).toBeInTheDocument();
  });

  it("handles onSelectionChange callback with empty selection", () => {
    const onSelectionChange = jest.fn();
    const callbackProps = { ...defaultProps, onSelectionChange };

    render(<CardDropdown {...callbackProps} />);

    // The callback would be triggered by child components
    expect(onSelectionChange).not.toHaveBeenCalled();
  });

  it("renders tooltip content for truncated vessel names", () => {
    const manySelectedProps = {
      ...defaultProps,
      selectedItems: [
        "Vessel 1",
        "Vessel 2",
        "Vessel 3",
        "Vessel 4",
        "Vessel 5",
      ],
      width: "200px",
    };

    render(<CardDropdown {...manySelectedProps} />);

    // Find the "+X more" badge with dynamic calculation
    const moreBadge = screen.getByText(/\+\d+ more/);
    expect(moreBadge).toHaveAttribute(
      "data-tooltip",
      "Vessel 1, Vessel 2, Vessel 3, Vessel 4, Vessel 5"
    );
  });

  it("handles keyboard navigation", () => {
    render(<CardDropdown {...defaultProps} />);

    const dropdownHeader = screen.getByRole("button");

    // Test Enter key
    fireEvent.keyDown(dropdownHeader, { key: "Enter", code: "Enter" });
    // Since we're mocking the toggle function, we can't test the actual behavior
    // but we can ensure the component doesn't crash
    expect(dropdownHeader).toBeInTheDocument();
  });
});
