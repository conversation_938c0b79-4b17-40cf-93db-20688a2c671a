import React from "react";
import { render } from "@testing-library/react";
import {
  AlertIcon,
  EnlargeIcon,
  ExternalLinkIcon,
  InfoIcon,
  RaCheckedIcon,
  RaUncheckedIcon,
} from "../../DashboardWidget/iCard/svgIcons";

describe("SVG Icons", () => {
  it("renders EnlargeIcon with correct props", () => {
    const { container } = render(<EnlargeIcon className="test-class" />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "18");
    expect(svg).toHaveAttribute("height", "19");
    expect(svg).toHaveClass("test-class");
  });

  it("renders ExternalLinkIcon with correct props", () => {
    const { container } = render(<ExternalLinkIcon className="test-class" />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "20");
    expect(svg).toHaveAttribute("height", "21");
    expect(svg).toHaveClass("test-class");
  });

  it("renders InfoIcon with correct dimensions", () => {
    const { container } = render(<InfoIcon className="info-class" />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "48");
    expect(svg).toHaveAttribute("height", "48");
  });

  it("renders RaCheckedIcon with correct props", () => {
    const { container } = render(<RaCheckedIcon className="test-class" />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "20");
    expect(svg).toHaveAttribute("height", "19");
    expect(svg).toHaveClass("test-class");
  });

  it("renders RaUncheckedIcon with correct dimensions", () => {
    const { container } = render(
      <RaUncheckedIcon className="unchecked-class" />
    );
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "20");
    expect(svg).toHaveAttribute("height", "20");
  });

  it("renders AlertIcon with correct dimensions", () => {
    const { container } = render(<AlertIcon className="alert-class" />);
    const svg = container.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("width", "48");
    expect(svg).toHaveAttribute("height", "48");
  });
});
