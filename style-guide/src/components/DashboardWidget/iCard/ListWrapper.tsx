import React from "react";
import "./styles/CardTable.scss";
import classNames from "classnames";
import { Vessel } from "../types/card-types";
import NoDataFound from "./NoDataFound";
interface CardResponsiveProps {
  isLoading?: boolean;
  data?: Vessel[];
  readonly responsiveConfig: {
    component: React.ComponentType<{ data: Vessel }>;
  };
  responsiveCardListContainerHeight?: string;
  configKey?: string;
}

function ListWrapper({
  isLoading = false,
  data = [],
  responsiveConfig,
  responsiveCardListContainerHeight,
  configKey,
}: Readonly<CardResponsiveProps>) {
  const cards = [...Array(10)].map((_, i) => i + 1);
  const { component: CardComponent } = responsiveConfig;

  const getListContent = () => {
    return data.length ? (
      data.map((item) =>
        CardComponent ? <CardComponent data={item} key={`${item.id}`} /> : null
      )
    ) : (
      <NoDataFound configKey={configKey} />
    );
  };

  return (
    <div
      style={
        responsiveCardListContainerHeight
          ? { minHeight: responsiveCardListContainerHeight }
          : {}
      }
      className={classNames("ra-tableContainer", "table-responsive", {
        "overflow-hidden": isLoading,
      })}
    >
      {isLoading
        ? cards.map((num) => (
            <div className="ra-card-resp" key={num}>
              <div className="ra-card-content">
                <div className="ra-card-header">
                  <div className="skeleton-header-cell" />
                </div>
                <div className="ra-detail-wrapper">
                  <div className="skeleton-header-cell" />
                </div>
                <div className="skeleton-header-cell" />
              </div>
            </div>
          ))
        : getListContent()}
    </div>
  );
}

export default ListWrapper;
