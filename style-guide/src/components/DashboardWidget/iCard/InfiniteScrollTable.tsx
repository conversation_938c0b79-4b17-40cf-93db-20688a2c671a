import React, { useRef, useMemo, useEffect, useState } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  ColumnDef,
  ExpandedState,
  getExpandedRowModel,
} from "@tanstack/react-table";
import classNames from "classnames";
import Spinner from "./Spinner";
import "./styles/CardTable.scss";
import NoDataFound from "./NoDataFound";
import { OverlayTrigger, Tooltip } from "react-bootstrap";

export interface InfiniteScrollTableProps<T extends object> {
  data: T[];
  columns: ColumnDef<T>[];
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  pagination: any;
  sorting: {
    sorting: Array<{ id: string; desc: boolean }>;
    onSortingChange: (params: Array<{ id: string; desc: boolean }>) => void;
  };
  tableContainerClassName?: string; // New prop for custom class
  tableDataRowClassName?: string;
  rowSelection?: Record<string, boolean>;
  fetchNextPage: () => void;
  subRowKey?: any;
  onRowClick?: (rowData: T, configKey?: string, ga4EventTrigger?: any) => void;
  configKey?: string;
  ga4EventTrigger?: any;
}

function InfiniteScrollTable<T extends object>({
  data: initialData,
  columns,
  isLoading,
  isFetchingNextPage,
  pagination,
  sorting: sortingProps,
  tableContainerClassName, // Use the new prop
  onRowClick,
  configKey,
  ga4EventTrigger,
  fetchNextPage,
}: Readonly<InfiniteScrollTableProps<T>>) {
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const [expanded, setExpanded] = useState<ExpandedState>({});

  const data = useMemo(() => initialData, [initialData]);

  const renderSkeletonRow = (columns: ColumnDef<any>[], key: number) => (
    <tr key={key} className="ra-tableRow">
      {columns.map((column) => (
        <td key={column.id}>
          <div className="skeleton-cell" />
        </td>
      ))}
    </tr>
  );

  const handleSortingChange = React.useCallback(
    (updater: any) => {
      const newSorting =
        typeof updater === "function" ? updater(sortingProps.sorting) : updater;
      sortingProps.onSortingChange(newSorting);
    },
    [sortingProps.onSortingChange, sortingProps.sorting]
  );

  const table = useReactTable({
    data,
    columns,
    state: {
      expanded,
      // Use the sorting state from props
      sorting: sortingProps.sorting,
    },
    manualSorting: true,
    enableExpanding: true,
    onExpandedChange: setExpanded,
    // Use the sorting change handler from props
    onSortingChange: handleSortingChange,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
  });

  const fetchMoreOnBottomReached = React.useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement) {
        const { scrollHeight, scrollTop, clientHeight } = containerRefElement;

        if (scrollHeight > clientHeight) {
          const scrolledToBottom =
            scrollHeight - scrollTop - clientHeight < 300;

          if (
            scrolledToBottom &&
            !isFetchingNextPage &&
            pagination.page < pagination.totalPages
          ) {
            fetchNextPage();
          }
        }
      }
    },
    [fetchNextPage, isFetchingNextPage, pagination.page, pagination.totalPages]
  );

  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isFetchingNextPage) {
        fetchMoreOnBottomReached(tableContainerRef.current);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [fetchMoreOnBottomReached, initialData, isFetchingNextPage]);

  const tableContent = useMemo(() => {
    if (isLoading) {
      const skeletonRows = Array.from({ length: 10 }, (_, index) =>
        renderSkeletonRow(columns, index)
      );
      return <tbody>{skeletonRows}</tbody>;
    }

    const renderRowContent = (cell, row) => {
      const { isSticky, stickySide = "", paddingRight } = (cell.column.columnDef
        .meta ?? {}) as Record<string, unknown>;
      const cellClassName = classNames({
        "ra-sticky-left": isSticky && stickySide === "left",
        "ra-sticky-right": isSticky && stickySide === "right",
        "ra-vesselNameCell": cell.column.id === "vessel",
        "pr-8px": true,
        "pr-48px": paddingRight === "48",
      });

      const rawCellValue = cell.getValue();
      let maxLength: number | null = null;

      if (cell.column.id === "task_required") {
        maxLength = 50;
      } else if (cell.column.id === "vessel") {
        maxLength = 15;
      }

      const needsTruncation =
        maxLength !== null &&
        typeof rawCellValue === "string" &&
        rawCellValue.length > maxLength;

      const cellContent = needsTruncation ? (
        // Render a span with the truncated text and a title for hover
        <OverlayTrigger
          placement="top"
          overlay={
            <Tooltip
              style={{ background: "none", paddingBottom: "6px" }}
              id={`tooltip-${cell.id}`}
            >
              {rawCellValue}
            </Tooltip>
          }
          show
        >
          <span style={{ cursor: "pointer" }}>
            {`${rawCellValue.substring(0, maxLength! - 3)}...`}
          </span>
        </OverlayTrigger>
      ) : (
        flexRender(cell.column.columnDef.cell, cell.getContext())
      );

      return (
        <td
          key={cell.id}
          className={cellClassName}
          onClick={() => {
            cell.column.id === "vessel" &&
              onRowClick?.(row.original, configKey, ga4EventTrigger);
          }}
        >
          {cellContent}
        </td>
      );
    };

    return (
      <tbody>
        {table.getRowModel().rows.map((row) => (
          <tr
            key={row.id}
            className={classNames(
              "ra-tableRow"
              // We removed the onRowClick handler from here
              // typeof onRowClick === "function" && "ra-clickable-row"
            )}
            // And removed the onClick handler from here as well
            // onClick={() => onRowClick?.(row.original)}
          >
            {row.getVisibleCells().map((cell) => renderRowContent(cell, row))}
          </tr>
        ))}
        {isFetchingNextPage && (
          <tr>
            <td colSpan={columns.length} className="ra-spinnerContainer">
              <Spinner />
            </td>
          </tr>
        )}
      </tbody>
    );
  }, [
    isLoading,
    table.getRowModel().rows,
    onRowClick,
    isFetchingNextPage,
    columns,
  ]);

  const isTableEmpty = !isLoading && table.getRowModel().rows.length === 0;

  return (
    <div
      ref={tableContainerRef}
      onScroll={(e) => {
        fetchMoreOnBottomReached(e.currentTarget);
      }}
      className={classNames(
        "ra-tableContainer",
        "table-responsive",
        tableContainerClassName,
        {
          "overflow-hidden": isLoading,
        }
      )}
    >
      {!isTableEmpty ? (
        <table
          className={classNames("ra-table", {
            "ra-dashed-border": isTableEmpty,
            "h-100": isLoading,
          })}
        >
          {isLoading ? (
            <thead className="ra-tableHeader">
              <tr>
                {columns.map((column, index) => (
                  <th key={column.id}>
                    <div className="skeleton-header-cell" />
                  </th>
                ))}
              </tr>
            </thead>
          ) : (
            <thead className="ra-tableHeader">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const {
                      isSticky,
                      stickySide = "",
                      headerAlign,
                      paddingRight,
                    } = (header.column.columnDef.meta ?? {}) as Record<
                      string,
                      unknown
                    >;
                    const headerClassName = classNames("th", {
                      "ra-sticky-left": isSticky && stickySide === "left",
                      "ra-sticky-right": isSticky && stickySide === "right",
                      "text-center": headerAlign === "center",
                      "text-end": headerAlign === "right",
                      "pr-8px": header.column.id !== "action",
                      "pr-48px": paddingRight === "48",
                    });
                    return (
                      <th
                        key={header.id}
                        onClick={header.column.getToggleSortingHandler()}
                        className={headerClassName}
                      >
                        <div
                          className={classNames(
                            "ra-header-content",
                            `justify-content-${
                              headerAlign && typeof headerAlign === "string"
                                ? headerAlign
                                : "start"
                            }`
                          )}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>
          )}
          {tableContent}
        </table>
      ) : (
        <NoDataFound configKey={configKey} />
      )}
    </div>
  );
}

export default InfiniteScrollTable;
