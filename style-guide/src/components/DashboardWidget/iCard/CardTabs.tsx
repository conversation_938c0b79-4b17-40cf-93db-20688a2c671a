import React, { useMemo } from "react";
import classNames from "classnames";
import "./styles/CardContainer.scss";
import { getGACategory } from "../util/util";

interface VesselTabsProps {
  tabs: string[];
  activeTab: string;
  IsAllTabVisible?: boolean;
  isLoading?: boolean;
  configKey?: string;
  ga4EventTrigger?: any;
  onTabChange: (tab: string) => void;
}

export const CardTabs: React.FC<VesselTabsProps> = ({
  tabs,
  activeTab,
  IsAllTabVisible,
  isLoading,
  configKey,
  ga4EventTrigger,
  onTabChange,
}) => {
  const handleTabChange = (tab: string) => {
    // GA Tagging logic
    const gaCategory = getGACategory(configKey);
    if (ga4EventTrigger) {
      ga4EventTrigger(gaCategory, tab, "Switch Tab");
    }

    // Call the original onTabChange handler
    onTabChange(tab);
  };

  const displayTabs = useMemo(() => {
    const cleanTabs = tabs.filter((t) => t !== "All");
    return IsAllTabVisible ? ["All", ...cleanTabs] : cleanTabs;
  }, [tabs, IsAllTabVisible]);

  if (isLoading) {
    return (
      <div className="ra-tabs-container">
        {displayTabs.map((tab) => (
          <div key={tab} className="ra-tab-skeleton skeleton"></div>
        ))}
      </div>
    );
  }
  if (!tabs || tabs.length === 0) {
    return null;
  }

  return (
    <div>
      <div className="ra-tabs-container">
        {displayTabs.map((tab) => (
          <button
            key={tab}
            onClick={() => handleTabChange(tab)}
            className={classNames("ra-tab-button", {
              active: activeTab === tab,
            })}
          >
            {tab}
          </button>
        ))}
      </div>
    </div>
  );
};
