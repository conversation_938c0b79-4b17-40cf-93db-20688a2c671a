import React, {
  useState,
  useMemo,
  useCallback,
  useRef,
  useLayoutEffect,
} from "react";
import { ChevronDown } from "lucide-react";
import classNames from "classnames";
import { CardDropdownMenu } from "./CardDropdownMenu";
import { useDropdown } from "../hooks/useDropdown";
import {
  CardDropdownProps,
  VesselGroup,
  VesselOption,
} from "../types/card-types";
import "./styles/CardDropdown.scss";

export function CardDropdown({
  groups = [],
  selectedItems,
  onSelectionChange,
  placeholder = "My Vessels",
  width,
  isSearchBoxVisible = true,
  isSelectAllVisible = true,
}: Readonly<CardDropdownProps>) {
  const { dropdownRef, isOpen, toggleDropdown } = useDropdown();
  const [searchTerm, setSearchTerm] = useState("");
  const displayTextRef = useRef<HTMLDivElement>(null);
  const [maxVisibleVessels, setMaxVisibleVessels] = useState(3);

  // allVessels,  is a flat array of all vessel names from all groups. It's used to manage the "Select All" functionality.
  // filteredGroups: This is the data used to render the list of vessels in the dropdown menu. It applies the search filter.
  // filteredVessels: This is a flat array of vessel names that are currently visible after filtering.
  // isAllSelected: This boolean checks if every vessel name in filteredVessels is also present in the selectedItems array.
  const { filteredGroups, filteredVessels, isAllSelected } = useMemo(() => {
    const lowerCaseSearchTerm = searchTerm.toLowerCase();
    const filtered = groups
      .map((group: VesselGroup) => ({
        ...group,
        vessels: group.vessels.filter((vessel: VesselOption) =>
          vessel.name.toLowerCase().includes(lowerCaseSearchTerm)
        ),
      }))
      .filter((group) => group.vessels.length > 0);

    const all = groups.flatMap((g: VesselGroup) =>
      g.vessels.map((v) => v.name)
    );

    // Get only the filtered/visible vessel names
    const filteredVesselNames = filtered.flatMap((g: VesselGroup) =>
      g.vessels.map((v) => v.name)
    );

    // Check if all filtered vessels are selected (not all vessels)
    const allFilteredAreSelected =
      filteredVesselNames.length > 0 &&
      filteredVesselNames.every((vName) => selectedItems.includes(vName));

    return {
      filteredGroups: filtered,
      allVessels: all,
      filteredVessels: filteredVesselNames,
      isAllSelected: allFilteredAreSelected,
    };
  }, [groups, searchTerm, selectedItems]);

  const handleToggleVessel = useCallback(
    (vesselName: string) => {
      const newSelected = selectedItems.includes(vesselName)
        ? selectedItems.filter((v) => v !== vesselName)
        : [...selectedItems, vesselName];
      onSelectionChange(newSelected);
    },
    [selectedItems, onSelectionChange]
  );

  const handleToggleGroup = useCallback(
    (group: VesselGroup) => {
      const groupVesselNames = group.vessels.map((v) => v.name);
      const allInGroupSelected = groupVesselNames.every((vName) =>
        selectedItems.includes(vName)
      );
      const newSelected = allInGroupSelected
        ? selectedItems.filter((v) => !groupVesselNames.includes(v))
        : Array.from(new Set([...selectedItems, ...groupVesselNames]));
      onSelectionChange(newSelected);
    },
    [selectedItems, onSelectionChange]
  );

  const handleToggleAll = useCallback(() => {
    if (isAllSelected) {
      // If all filtered vessels are selected, unselect only the filtered vessels
      const newSelected = selectedItems.filter(item => !filteredVessels.includes(item));
      onSelectionChange(newSelected);
    } else {
      // If not all filtered vessels are selected, select all filtered vessels
      const newSelected = Array.from(new Set([...selectedItems, ...filteredVessels]));
      onSelectionChange(newSelected);
    }
  }, [isAllSelected, filteredVessels, selectedItems, onSelectionChange]);

  // Utility function to measure text width
  const measureTextWidth = useCallback(
    (
      text: string,
      fontSize = "14px",
      fontFamily = "Arial, sans-serif"
    ): number => {
      // Detect test environment by checking for Jest globals or JSDOM
      const isTestEnvironment =
        (typeof process !== "undefined" && process.env.NODE_ENV === "test") ||
        (typeof global !== "undefined" && global.hasOwnProperty("__JSDOM__")) ||
        (typeof window !== "undefined" &&
          window.navigator.userAgent.includes("jsdom"));

      // Fallback for test environments where canvas is not available
      if (
        isTestEnvironment ||
        typeof document === "undefined" ||
        !document.createElement
      ) {
        return text.length * 8; // Rough approximation: 8px per character
      }

      try {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        if (!context) {
          return text.length * 8; // Fallback approximation
        }

        context.font = `${fontSize} ${fontFamily}`;
        return context.measureText(text).width;
      } catch (error) {
        // Fallback for environments where canvas is not supported (like Jest/JSDOM)
        return text.length * 8; // Rough approximation: 8px per character
      }
    },
    []
  );

  // Calculate maximum visible vessels based on available space
  useLayoutEffect(() => {
    if (!displayTextRef.current || selectedItems.length === 0) {
      return;
    }

    const container = displayTextRef.current;
    const containerWidth = container.offsetWidth;

    // If container width is 0 (like in test environments), use a default width
    const effectiveWidth = containerWidth > 0 ? containerWidth : 300;

    // Reserve space for chevron icon and padding (approximately 40px)
    const reservedSpace = 40;
    // Reserve space for "+X more" badge (approximately 80px)
    const moreBadgeSpace = 80;
    const availableWidth = effectiveWidth - reservedSpace;

    let totalWidth = 0;
    let maxVisible = 0;

    const canFitItem = (itemWidth: number) =>
      totalWidth + itemWidth <= availableWidth;
    const needsMoreBadge = (index: number) => index < selectedItems.length - 1;

    for (let i = 0; i < selectedItems.length; i++) {
      const vesselName = selectedItems[i];
      // Add comma and space width for all items except the first

      const separatorWidth = i > 0 ? measureTextWidth(", ") : 0;
      const itemWidth = measureTextWidth(vesselName) + separatorWidth;
      // Check if we can fit this item

      if (canFitItem(itemWidth)) {
        totalWidth += itemWidth;
        maxVisible++;
        continue;
      }
      // We have more items, so we need to reserve space for "+X more"

      if (
        needsMoreBadge(i) &&
        totalWidth + moreBadgeSpace > availableWidth &&
        maxVisible > 0
      ) {
        // Remove the last item to make space for "+X more"

        maxVisible--;
      }
      break;
    }

    // Ensure we show at least 1 vessel if there are selected items
    setMaxVisibleVessels(Math.max(1, maxVisible));
  }, [selectedItems, measureTextWidth]);

  const displayText = useMemo(() => {
    if (selectedItems.length === 0) {
      return <span className="raPlaceholderText">{placeholder}</span>;
    }

    const isOverLimit = selectedItems.length > maxVisibleVessels;

    if (isOverLimit) {
      const visibleVessels = selectedItems
        .slice(0, maxVisibleVessels)
        .join(", ");
      const moreCount = selectedItems.length - maxVisibleVessels;
      const tooltipContent = selectedItems.join(", ");

      return (
        <>
          <span className="raVesselNames">{visibleVessels}</span>
          <span className="raMoreBadge" data-tooltip={tooltipContent}>
            +{moreCount} more
          </span>
        </>
      );
    }

    return <span className="raVesselNames">{selectedItems.join(", ")}</span>;
  }, [selectedItems, placeholder, maxVisibleVessels]);

  return (
    <div className={classNames("raDropdownContainer")} ref={dropdownRef}>
      <button
        className="raDropdownHeader"
        onClick={toggleDropdown}
        aria-label={placeholder}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
      >
        <div className="raDisplayTextContainer" ref={displayTextRef}>
          {displayText}
        </div>
        <ChevronDown
          size={16}
          aria-label="toggle-chevron"
          className={classNames("raChevron", {
            ["raRotate"]: isOpen,
          })}
        />
      </button>

      {isOpen && (
        <CardDropdownMenu
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filteredGroups={filteredGroups}
          selectedItems={selectedItems}
          onToggleVessel={handleToggleVessel}
          onToggleGroup={handleToggleGroup}
          isAllSelected={isAllSelected}
          onToggleAll={handleToggleAll}
          isSearchBoxVisible={isSearchBoxVisible}
          isSelectAllVisible={isSelectAllVisible}
        />
      )}
    </div>
  );
}
