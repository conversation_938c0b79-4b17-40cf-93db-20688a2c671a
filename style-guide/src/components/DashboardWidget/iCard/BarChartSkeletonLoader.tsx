import React from "react";
import "./styles/BarChartSkeleton.scss";

interface BarChartSkeletonLoaderProps {
  readonly isMobileOrTablet?: boolean;
}

export default function BarChartSkeletonLoader({
  isMobileOrTablet,
}: Readonly<BarChartSkeletonLoaderProps>) {
  const tickCount = isMobileOrTablet ? 4 : 10;
  const allXAxisLabels = [0, 25, 50, 75, 100, 125, 150, 175, 200, 225];
  const labelsToShow = isMobileOrTablet
    ? allXAxisLabels.slice(0, 4)
    : allXAxisLabels;

  const yAxisTicks = Array.from({ length: tickCount }, (_, index) => ({
    id: `y-tick-${index}`,
  }));

  return (
    <div className="ra-vessel-bar-chart-wrapper">
      <div className="chart-container">
        <div className="chart-content-skeleton">
          <div className="y-axis-ticks-container">
            {yAxisTicks.map((tick) => (
              <div className="y-axis-tick" key={tick.id}></div>
            ))}
          </div>
          <div className="bar-rows-container">
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
            <div className="bar-row">
              <div className="label-loader"></div>
              <div className="bar-wrapper">
                <div className="bar-loader"></div>
              </div>
            </div>
          </div>
          <div className="x-axis-labels">
            {labelsToShow.map((label) => (
              <div className="x-axis-label" key={label}>
                {label}
              </div>
            ))}
          </div>
        </div>
        <div className="bar-chart-legend">
          <div className="bar-chart-legend-item">
            <span className="color-box overdue"></span>Overdue
          </div>
          <div className="bar-chart-legend-item">
            <span className="color-box due-30"></span>Due within 30 days
          </div>
          <div className="bar-chart-legend-item">
            <span className="color-box due-60"></span>Due within 60 days
          </div>
        </div>
      </div>
    </div>
  );
}
