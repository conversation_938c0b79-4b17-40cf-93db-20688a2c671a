import React from "react";
import { InfoIcon } from "./svgIcons";
import { WidgetConstant } from "../types/widget.constant";

interface NoDataFoundProps {
  readonly configKey?: string;
}

const NoDataFound = ({ configKey }: NoDataFoundProps) => {
  const isRisk = configKey === WidgetConstant.RISK_ASSESSMENT;
  return (
    <div className="no-data-found-wrapper">
      <div className="no-data-found">
        <InfoIcon />
        {isRisk ? (
          <p className="text-muted fs-14 text-center">
            There are no pending Risk Assessment <br />
            for review. You’re fully up to date.
          </p>
        ) : (
          <p className="text-muted fs-14">No Data Available</p>
        )}
      </div>
    </div>
  );
};

export default NoDataFound;
