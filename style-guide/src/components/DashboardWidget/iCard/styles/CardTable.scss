@use "./variables" as *;
@use "./mixins" as *;

.cardTableContainer {
  height: 380px;
}

.ra-tableContainer {
  position: relative;
  overflow-y: auto;
  height: 380px;
}

.ra-table {
  width: 100%;
  table-layout: auto;
  border-collapse: collapse;
  border-spacing: 10px 0;
  tr > td:first-child {
    border: 0px !important;
  }
  tr > th:first-child {
    border: 0px !important;
  }

  .ra-tableHeader {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
    white-space: nowrap;

    tr {
      font-size: 1.01rem !important;
      color: #000000;
    }

    th {
      padding: 0.5rem 0;
      font-weight: 600;
      cursor: pointer;
      user-select: none;
      text-wrap: auto;
    }
  }

  .ra-tableRow {
    border-top: 1px solid #e5e7eb;
    white-space: nowrap;
    font-size: 14px;

    td {
      padding: 0.5rem 0;
      text-wrap: auto;
    }
  }
}

.ra-headerContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.ra-sortIcon {
  &.neutral {
    color: #a1a1aa;
  }
  &.active {
    color: $color-fleet-blue;
  }
}

.ra-actionHeader {
  &.nonSortable {
    cursor: default;
  }
}

.ra-textLeft {
  text-align: left;
  .ra-headerContent {
    justify-content: flex-start;
  }
}

.ra-vesselNameCell {
  font-weight: 600;
  text-align: left !important;
  text-decoration: underline;
  text-underline-offset: 2px !important;
  color: $color-fleet-blue;
  cursor: pointer;

  .ra-cardNameButton {
    color: $color-fleet-blue;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    font: inherit;
    font-size: 1rem !important;
    text-decoration: underline;
    text-underline-offset: 2px;
    outline: none;
    text-align: left;
    all: unset !important;
  }
}

.ra-vesselSecondColumnEntry {
  padding: 0.5rem 0;
}

.ra-badge {
  color: var(--badge-text-color, white);
  background-color: var(--badge-bg-color, #808080);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem !important;
  display: inline-block;
}

.ra-statusBadge {
  display: inline-block;
  padding: 4px 10px !important;
  font-size: 0.9rem;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 5px;
  background-color: rgba(var(--status-rgb, 128, 128, 128), 0.1);
  color: rgb(var(--status-rgb, 128, 128, 128));
}

.ra-approvedBadge {
  background-color: #e8f5e9;
  color: #4caf50;
}

.ra-approvedWithConditionBadge {
  background-color: #f1f8e9;
  color: #8bc34a;
}

.ra-rejectedBadge {
  background-color: #ffebee;
  color: #f44336;
}

.ra-emailCell {
  text-align: center;
}

.action-button-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .action-button {
    color: $color-fleet-blue;
    cursor: pointer;
    padding: 0.25rem 0;
    background: none;
    border: none;
    outline: none;
  }

  .ra-tooltip {
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background: black;
    color: white;
    font-weight: 300;
    font-size: 1.1rem !important;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
    z-index: 10;

    &::after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 6px solid transparent;
      border-top-color: black;
    }
  }

  &:hover .ra-tooltip {
    opacity: 1;
  }
}

.ra-spinnerContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.ra-statusCell {
  text-align: center;
  padding: 4rem 0;
  font-size: 1rem;
  color: #6b7280;
  height: 200px;
}

@media (max-width: 768px) {
  .ra-tableContainer {
    max-height: calc(100vh - 200px) !important;
    overflow-x: auto;
    padding-right: 5px;
  }
  .ra-table {
    .ra-tableHeader th,
    .ra-tableRow td {
      padding: 0.5rem 0.25rem;
    }
    .ra-tableHeader tr {
      font-size: 0.75rem;
    }
    .ra-tableRow .ra-badge,
    .ra-statusBadge {
      padding: 0.15rem 0.5rem;
      font-size: 0.65rem !important;
    }
  }
}

.ra-header-content {
  font-size: 14px;
  font-weight: 600;
}

.skeleton-cell {
  width: 95%;
  height: 25px;
  margin: 5px 0;
  border-radius: 4px;
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-header-cell {
  height: 25px;
  width: 95%;
  border-radius: 4px;
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.fs-14 {
  font-size: 14px;
}

.table-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
}

.no-data-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.no-data-found-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.ra-card-resp {
  padding-bottom: 10px;
  margin-top: 6px;
  border-bottom: 1px solid #dee2e6;
}

.pr-8px {
  padding-right: 8px !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.pr-48px {
  padding-right: 48px !important;
}

.tooltip-wrapper {
  .tooltip{
    background: none !important;
  }
}
