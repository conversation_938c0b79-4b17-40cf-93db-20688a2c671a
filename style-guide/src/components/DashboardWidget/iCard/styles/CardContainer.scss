@use "./variables" as *;

.ra-vessel-selects-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.ra-vessel-selects-container .ra-selectWrapper {
  flex: 1 1 auto;
  min-width: 200px;
  max-width: 100%;
}

.ra-vessel-card-container {
  padding: 0.8rem 1rem;
  display: flex;
  flex-direction: column;
  border: 1px solid #dee2e6 !important;
  border-radius: 5.2px !important;
  box-shadow: 0px 0px 3.47px 0px rgba(0, 0, 0, 0.149);
  background: #ffffff;

  &.size-sm {
    height: 400px;
  }

  &.size-md {
    height: 490px;
  }

  &.size-lg {
    height: 720px;
  }

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.ra-vessel-card-modal-overlay {
  all: unset;
  position: fixed;
  inset: 0;
  z-index: 1031;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  cursor: pointer;
  padding: 0;
  border: none;
}

.ra-vessel-card-modal {
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 1rem;
  position: relative;
  cursor: auto;

  &.size-sm {
    min-width: 520px;
  }

  &.size-md {
    min-width: 833px;
  }

  &.size-lg {
    min-width: 1000px;
  }
}

.ra-vesel-widget-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.ra-vessel-module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

.ra-vessel-module-title {
  font-size: 16px;
  font-weight: 600;
  color: $color-fleet-blue;
}

.ra-vessel-module-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.ra-view-toggle-container {
  background-color: color(display-p3 0.9569 0.9647 0.9725);
  padding: 0.3rem;
  border-radius: 0.25rem;
  display: flex;
  gap: 0.2rem;
  align-items: center;
}

.ra-view-toggle-button {
  width: 20px;
  height: 20px;
  padding: 3px;
  border-radius: 0.25rem;
  border: none;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease-in-out, fill 0.2s ease-in-out;

  &.active {
    background-color: $color-fleet-blue;
    color: white;
  }

  &:not(.active) {
    fill: $color-fleet-blue;

    &:hover {
      background-color: #bfdbfe;
    }
  }
}

.ra-view-toggle-button-modal {
  width: 24px;
  height: 24px;
  padding: 4.5px;
}

.ra-view-toggle-icon {
  width: auto;
  height: auto;
}

.ra-enlarge-icon {
  width: 20px;
  height: 20px;
  color: $color-fleet-blue;
  margin-left: 0.5rem;
  cursor: pointer;
}

.ra-minimize-icon {
  width: 24px;
  height: 24px;
  color: $color-fleet-blue;
  margin-left: 0.5rem;
  cursor: pointer;
}
.ra-last-updated-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.2rem;
  margin-top: 0.2rem;
}

.ra-last-updated-text {
  font-size: 12px;
  font-weight: 400;
  color: #6c757d;
  display: flex;
  align-items: center;
  margin-top: -10px;
  margin-bottom: 5px;
}

.ra-refresh-icon {
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 0.3rem;
  color: $color-fleet-blue;
  cursor: pointer;
}

.ra-tabs-container {
  display: flex;
  font-weight: 200;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  border-bottom: 1px solid #e5e7eb;

  @media (max-width: 640px) {
    overflow-x: scroll;
    gap: 0.5rem;
    ::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

.ra-tab-button {
  background: none;
  color: rgba(108, 117, 125, 1);
  font-size: 14px;
  border: none;
  position: relative;
  padding-top: 0.4rem;
  padding-right: 0.4rem;
  padding-left: 0.4rem;
  padding-bottom: 0.5rem;
  cursor: pointer;

  &:focus {
    outline: none;
  }

  &.active {
    border-bottom: 1px solid $color-fleet-blue ;
    color: $color-fleet-blue;
    font-weight: 500;
  }
}
.ra-active-tab-indicator {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 2px;
  background-color: $color-fleet-blue;
}

.ra-content-container {
  flex-grow: 1;
  overflow: hidden;
  position: relative;
}

.ra-content-container-modal {
  height: 350px;
}

.ra-content-container-non-modal {
  height: 350px;
}

.ra-close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  color: #4b5563;
  cursor: pointer;

  &:hover {
    color: #000;
  }
}

.skeleton {
  background: linear-gradient(90deg, #edf3f7 25%, #ffffff 50%, #edf3f7 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.ra-tab-skeleton {
  width: 75px;
  height: 25px;
  margin-right: 10px;
  margin-bottom: 10px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.ra-modal-dialog {
  all: unset;
  position: fixed;
  inset: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
  background: none;
}


.ra-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  padding: 0;
  margin: 0;
  z-index: 999;
  cursor: pointer;
}



.ra-modal-overlay:focus {
  outline: none;
}

.ra-modal-content {
  position: fixed;
  z-index: 1000;
  background: white;
  border-radius: 8px;
  padding: 1rem;
  width: 90%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  &.size-sm {
    width: 520px;
    height: 420px;
  }

  &.size-md {
    width: 800px;
  }

  &.size-lg {
    width: 1000px;
    height: 720px;
  }
}
