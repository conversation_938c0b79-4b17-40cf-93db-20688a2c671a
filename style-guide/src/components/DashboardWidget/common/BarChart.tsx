import React, { useState, useRef, useEffect } from "react";
import {
  ScrollableChartElements,
  StickyXAxis,
  ChartTooltip,
  ChartLegend,
} from "./ChartComponents";
import { useD3Chart } from "../hooks/useD3Chart";
import "./styles/BarChart.scss";
import { Vessel } from "../types/card-types";
import * as d3 from "d3";
import { WidgetConstant } from "../types/widget.constant";
import { getSurveyActiveDueTab } from "../util/util";
import useResizeObserver from "../hooks/useResizeObserver";

interface BarChartProps {
  readonly vessels: any[];
  readonly width?: number;
  readonly heightPerBar?: number;
  readonly valueHeaders: string[];
  readonly badgeColors: string[];
  readonly valueDomain: [number, number];
  readonly isModal?: boolean;
  readonly configKey?: string;
  readonly ga4EventTrigger?: any;
  readonly activeTab?: string;
  readonly isMobileOrTablet?: boolean;
  readonly onSendEmail: (vessel: Vessel) => void;
  readonly onVesselClick: (vessel: Vessel) => void;
}

const LONG_PRESS_DURATION = 500; // 500ms

export default function BarChart({
  vessels,
  width: initialWidth = 700,
  heightPerBar = 55,
  valueHeaders,
  badgeColors,
  valueDomain,
  isModal = false,
  configKey,
  ga4EventTrigger,
  activeTab,
  isMobileOrTablet,
  ...rest
}: BarChartProps) {
  const MARGIN = {
    top: 15,
    right: isMobileOrTablet ? 30 : 50,
    bottom: 20,
    left: isMobileOrTablet ? 74 : 90,
  };
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartWidth, setChartWidth] = useState(initialWidth);
  const containerWidthFromHook = useResizeObserver(chartContainerRef);
  const [tooltip, setTooltip] = useState({
    visible: false,
    content: "",
    x: 0,
    y: 0,
  });

  const timerRef = useRef<NodeJS.Timeout | null>(null);
  useEffect(() => {
    if (containerWidthFromHook) {
      setChartWidth(containerWidthFromHook);
    }
  }, [containerWidthFromHook]);

  useEffect(() => {
    /**
     * This function handles the global touch end event.
     * It checks if the tooltip is currently visible and, if so, hides it.
     * This ensures the tooltip disappears when a user lifts their finger anywhere on the document,
     * providing a good user experience on touch devices.
     */
    const handleGlobalTouchEnd = () => {
      if (tooltip.visible) {
        setTooltip((prev) => ({ ...prev, visible: false }));
      }
    };

    /**
     * Add a global event listener for 'touchend'.
     * This listener is attached to the entire document, so it captures touch events
     * regardless of where they occur.
     */
    document.addEventListener("touchend", handleGlobalTouchEnd);

    /**
     * The cleanup function to remove the event listener.
     * This is crucial to prevent memory leaks. The listener is removed
     * when the component unmounts or when the `tooltip.visible` dependency changes.
     */
    return () => {
      document.removeEventListener("touchend", handleGlobalTouchEnd);
    };
  }, [tooltip.visible]);

  const generateTicks = () => {
    const [min, max] = valueDomain;
    const maxTicks = isMobileOrTablet ? 3 : 8;

    const tempScale = d3
      .scaleLinear()
      .domain([min, max])
      .range([0, 100])
      .nice(maxTicks);

    const ticks = tempScale.ticks(maxTicks);

    if (ticks[ticks.length - 1] < max) {
      ticks.push(max);
    }

    return ticks;
  };

  const ticks = generateTicks();
  const tickDiff =
    ticks.length >= 2 ? parseInt(ticks[1]) - parseInt(ticks[0]) : 0;
  // Filter out vessels where the sum of values for all headers is zero.
  const filteredVessels = vessels.filter((vessel) => {
    const totalCount = valueHeaders.reduce((sum, key) => {
      return sum + (vessel[key] || 0);
    }, 0);
    return totalCount > 0;
  });

  const {
    xScale,
    yScale,
    barColorScale,
    textColorScale,
    stackedBarData,
    chartHeight,
    totalHeight,
  } = useD3Chart({
    vessels: filteredVessels,
    valueHeaders,
    badgeColors,
    valueDomain: [0, ticks[ticks.length - 1]],
    chartWidth,
    heightPerBar,
    margin: MARGIN,
    isMobileOrTablet: isMobileOrTablet ?? false,
    tickDiff,
  });

  const handleTooltipContent = (segment: any) => {
    const badgeColorMap: { [key: string]: string } = {
      "#d80e61": "tooltip-color--pink",
      "#fbc02d": "tooltip-color--yellow",
      "#27a527": "tooltip-color--green",
    };

    const tooltipBadgeClass = badgeColorMap[barColorScale(segment.key)];
    return `
      <div class="tooltip-content">
        <div class="tooltip-title"><b>${segment.key}</b></div>
        <div class="tooltip-row">
          <span class="tooltip-color ${tooltipBadgeClass}"></span>
          ${segment.value}
        </div>
      </div>`;
  };

  const handleDesktopMouseOver = (event: React.MouseEvent, segment: any) => {
    const content = handleTooltipContent(segment);
    setTooltip({
      visible: true,
      content,
      x: event.clientX + 20,
      y: event.clientY,
    });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (tooltip.visible) {
      setTooltip((prev) => ({
        ...prev,
        x: event.clientX + 20,
        y: event.clientY,
      }));
    }
  };

  const handleMouseOut = () => {
    setTooltip((prev) => ({ ...prev, visible: false }));
  };

  const handleTouchStart = (event: React.TouchEvent, segment: any) => {
    if (isMobileOrTablet) {
      //clear the long press time
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      timerRef.current = setTimeout(() => {
        const content = handleTooltipContent(segment);
        setTooltip({
          visible: true,
          content,
          x: event.touches[0].clientX + 20,
          y: event.touches[0].clientY,
        });
      }, LONG_PRESS_DURATION);
    }
  };

  const handleTouchEnd = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  const handleContextMenu = (event: React.MouseEvent) => {
    // Prevent the browser's native context menu from appearing
    event.preventDefault();
  };

  const handleClick = (segment: any) => {
    setTooltip((prev) => ({ ...prev, visible: false }));
    if (configKey === WidgetConstant.SURVEYS_CERTIFICATES) {
      const tab = activeTab === "All" ? "all_types" : activeTab?.toLowerCase();
      const activeDueTab = getSurveyActiveDueTab(segment?.key);
      const url = `vessel/ownership/details/${segment?.vesselOwnershipId}/certificates?activeTab=${tab}&activeDueTab=${activeDueTab}`;

      if (ga4EventTrigger) {
        ga4EventTrigger(
          WidgetConstant.SURVEYS_CERTIFICATES,
          activeDueTab,
          "Click Bar Graph"
        );
      }

      window.open(url, "_blank");
    }
  };

  return (
    <div
      className={`ra-vessel-bar-chart-container ${
        isModal ? "bar-chart-modal-height" : ""
      }`}
    >
      <div ref={chartContainerRef} className="chart-scroll-container">
        <div
          className="chart-content"
          style={{ "--chart-width": `${chartWidth}px` } as React.CSSProperties}
        >
          <svg
            width={chartWidth}
            height={totalHeight}
            className="main-chart no-select" // no-select style is for not selecting the text while long press in mobile view
          >
            <g>
              <ScrollableChartElements
                yScale={yScale}
                xScale={xScale}
                height={chartHeight}
                margin={MARGIN}
                vessels={vessels}
                ticks={ticks}
              />
              {stackedBarData.map((bar) => (
                <g
                  key={bar.vesselIndex}
                  transform={`translate(${MARGIN.left}, 0)`}
                  // Prevent context menu on all bar segments(that right click on long press)
                  onContextMenu={handleContextMenu}
                >
                  {bar.segments.map((segment) => (
                    <g
                      key={segment.key}
                      className="bar-subgroup"
                      onClick={() => handleClick(segment)}
                      {...(!isMobileOrTablet
                        ? {
                            onMouseOver: (e) =>
                              handleDesktopMouseOver(e, segment),
                            onMouseMove: handleMouseMove,
                            onMouseOut: handleMouseOut,
                          }
                        : {
                            onTouchStart: (e) => handleTouchStart(e, segment),
                            onTouchEnd: handleTouchEnd,
                          })}
                    >
                      <rect
                        x={segment.x}
                        y={segment.y}
                        width={segment.width}
                        height={segment.height}
                        fill={barColorScale(segment.key)}
                      />
                      {segment.shouldDisplayValue && (
                        <text
                          x={segment.x + segment.width / 2}
                          y={segment.y + segment.height / 2}
                          dy=".35em"
                          textAnchor="middle"
                          fill={textColorScale(segment.key)}
                        >
                          {segment.value}
                        </text>
                      )}
                    </g>
                  ))}
                </g>
              ))}
            </g>
          </svg>
        </div>
      </div>

      <div className="xAxix-legend-parent">
        <StickyXAxis
          xScale={xScale}
          width={chartWidth}
          height={MARGIN.bottom}
          margin={{ left: MARGIN.left }}
          ticks={ticks}
        />
        <ChartLegend valueHeaders={valueHeaders} badgeColors={badgeColors} />
      </div>

      {tooltip.visible && (
        <ChartTooltip
          content={tooltip.content}
          position={{ x: tooltip.x, y: tooltip.y }}
          isVisible={tooltip.visible}
        />
      )}
    </div>
  );
}
