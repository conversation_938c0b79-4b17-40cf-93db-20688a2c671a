import React, { useRef, useEffect, useCallback } from "react";
import Highcharts from "highcharts";
import HighchartsReact from "highcharts-react-official";
import "highcharts/highcharts-3d";
import { useHighchartsDonut } from "../hooks/useHighchartsDonut";
import "./styles/DonutChartCard.scss";
import { useMediaQuery } from "../hooks/useMediaQuery";

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface DonutChartProps {
  data: ChartDataItem[];
  configKey?: string;
  ga4EventTrigger?: any;
  containerWidth?: number;
  isMediumScreen: boolean;
}

const Dynamic3DDonutChart: React.FC<DonutChartProps> = ({
  data,
  configKey,
  ga4EventTrigger,
  containerWidth = 300,
  isMediumScreen,
}) => {
  const isExtraLargeScreen = useMediaQuery("xl", "min");
  const isLargeScreen = useMediaQuery(null, "min", 1340);
  let pieChartSize = "30%";
  if (isMediumScreen) {
    pieChartSize = "100%";
  }
  if (isLargeScreen) {
    pieChartSize = "60%";
  }
  if (isExtraLargeScreen) {
    pieChartSize = "80%";
  }
  const { options, tooltipStyle } = useHighchartsDonut({
    data,
    size: pieChartSize,
    configKey,
    ga4EventTrigger,
    showLabels: !isMediumScreen,
  });

  const chartRef = useRef<HighchartsReact.RefObject>(null);
  const chartInstanceRef = useRef<Highcharts.Chart | null>(null);

  const setChartInstance = useCallback((chart: Highcharts.Chart) => {
    chartInstanceRef.current = chart;
  }, []);

  // Isolate tooltip hide logic to this chart's events only
  useEffect(() => {
    const chartContainer = chartInstanceRef.current?.container;
    if (!isMediumScreen || !chartContainer) {
      return;
    }

    const hideTooltip = () => {
      chartInstanceRef.current?.tooltip.hide();
    };

    // Listen on this chart's container, not the entire document
    chartContainer.addEventListener("touchend", hideTooltip);
    chartContainer.addEventListener("scroll", hideTooltip);

    return () => {
      chartContainer.removeEventListener("touchend", hideTooltip);
      chartContainer.removeEventListener("scroll", hideTooltip);
    };
  }, [isMediumScreen, chartInstanceRef.current]);
  
  // the contextmenu event listener directly on the chart container
  useEffect(() => {
    const chartContainer = chartInstanceRef.current?.container;
    if (!chartContainer) {
      return;
    }

    const handleContextMenu = (e: Event) => {
      e.preventDefault();
    };

    chartContainer.addEventListener("contextmenu", handleContextMenu);

    return () => {
      chartContainer.removeEventListener("contextmenu", handleContextMenu);
    };
  }, [chartInstanceRef.current]);

  // touch handlers to prevent default behavior and hide tooltip
  const handleTouchStart = (e: React.TouchEvent) => {
    // Keep this to prevent context menu and text selection
    // by doing this tooltip on click on mobile screen resolved
    chartInstanceRef.current?.tooltip.hide();
  };

  return (
    <div
      className="donut-chart-container"
      style={{ width: `${containerWidth - 10}px` }}
      onTouchStart={handleTouchStart} // Add touch handlers to the main container
    >
      <style>{tooltipStyle}</style>
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
        ref={chartRef}
        containerProps={{ className: "donut-chart-highchart" }}
        callback={setChartInstance}
      />
      <div className="legend-container">
        {data.map((item) => (
          <div className="legend-item" key={item.label}>
            <span
              className="legend-item-circle"
              style={{ background: item?.color || "#fff" }}
            />
            {item?.label ?? ""}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dynamic3DDonutChart;
