@use '../../iCard/styles/variables' as *;
@use '../../iCard/styles/mixins' as *;

.deficiency-count-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  height: 100%;

  @include media-max(md) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.deficiency-card-wrapper {
  max-height: 114px;
}

.deficiency-count-title {
  font-size: 14px;
  font-weight: 400;
  color: #333;
}

.deficiency-total-count {
  font-size: 20px;
  font-weight: bold;
  color: #1f4a70;
}

.vessel-grid-root {
  height: 300px;
  overflow-y: auto;
  position: relative;
}

.spinner-container {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}

.no-results-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1rem;
  color: #6b7280;
}

.donut-chart-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  min-height: 230px;

  @include media-max(md) {
    min-height: 265px;
  }

  .donut-chart-header {
    width: fit-content;
    margin-left: 14px;
    line-height: 0;
    margin-bottom: 5px;
  }
}

.donut-chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.donut-chart-content-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
}

.donut-chart-visual {
  justify-content: center;
  display: flex;
  position: relative;
}

.donut-chart-legend-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
}

.donut-chart-highchart {
  height: 208px;
  overflow: unset !important;
}

.donut-chart-container {
  width: 100%;
  height: 100%;
  .legend-container {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    padding: 0 8px;
    position: relative;
    row-gap: 8px;
    max-width: 840px;
  }

  .legend-item {
    font-size: 12px;
    color: black;
  }

  .legend-item-circle {
    width: 12px;
    height: 12px;
    display: inline-block;
    border-radius: 50%;
    top: 1px;
    position: relative;
    margin: 0 6px;
  }
}

@media (min-width: 1200px) and (max-width: 1372px) {
  .donut-chart-container .legend-container {
    bottom: 12px;
  }
}

.donut-chart-legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1.5rem;
  width: 100%;
}

.donut-chart-legend-item {
  display: flex;
  align-items: center;
}

.donut-chart-legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;

  &.color-red {
    background-color: red;
  }

  &.color-yellow {
    background-color: yellow;
  }

  &.color-green {
    background-color: green;
  }
}

.donut-chart-legend-label {
  font-weight: 500;
  color: #555;
  margin-right: 0.25rem;
  font-size: 0.8rem;
}

.donut-chart-legend-value {
  font-weight: bold;
  color: #333;
  font-size: 0.8rem;
}

.dashboard-grid-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto 1fr;
  gap: 12px;
  padding-top: 4px;
  height: 100%;

  @include media-max(md) {
    grid-template-columns: 1fr;
  }
}
