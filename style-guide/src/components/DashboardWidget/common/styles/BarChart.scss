svg {
  font-family: sans-serif;
  .domain {
    display: none;
  }
  .tick line {
    stroke: #ccc;
  }
  .tick text {
    fill: #000000;
    font-size: 12px !important;
    word-break: break-all;
  }
}

.xAxix-legend-parent {
  position: sticky;
  bottom: 0;
  z-index: 10;
  margin-top: 5px;
}

.x-axis-scroll-container {
  width: 100%;
  overflow-x: auto;
  height: 40px;

  .x-axis-container {
    background: white;
    margin-top: 20px;
    width: 100%;

    svg {
      display: block;
      min-width: 100%;
    }
  }
}

.ra-vessel-bar-chart-container {
  position: relative;
  width: 100%;
  height: 350px;
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  justify-content: end;
	@media (max-width: 350px) {
		height: 340px;
	}
}

.bar-chart-modal-height {
  height: 470px !important;
}

.custom-tooltip {
  position: fixed;
  background: rgb(0, 0, 0);
  color: white;
  border-radius: 4px;
  padding: 8px;
  font-weight: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s;
  margin-top: -50px;

  left: var(--tooltip-left);
  top: var(--tooltip-top);
  &--visible {
    opacity: 1;
  }

  &::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: black;
    filter: drop-shadow(-2px 0 1px rgba(0, 0, 0, 0.1));
  }

  .tooltip-title {
    font-weight: 300;
    font-size: 14px;
  }

  .tooltip-row {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 300;
    font-size: 14px;
  }

  .tooltip-color {
    display: inline-block;
    width: 12px !important;
    height: 12px !important;
    border-radius: 12px;
  }
  .tooltip-color--pink {
    background-color: #d80e61 !important;
  }

  .tooltip-color--yellow {
    background-color: #fbc02d !important;
  }

  .tooltip-color--green {
    background-color: #27a527 !important;
  }
}

.bar-group {
  cursor: pointer;
}

.chart-scroll-container {
  overflow: auto;
  overflow-x: hidden;
  position: relative;
}

.main-chart {
  display: block;
  margin-top: -14px;
}

.x-axis-container {
  background: white;
  margin-bottom: 30px;
}

.tooltip {
  position: absolute;
  padding: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  pointer-events: none;
}

.bar-subgroup {
  cursor: pointer;

  text {
    font-size: 10px;
    font-weight: 500;
  }
}

.y-axis text {
  font-size: 14px;
  font-weight: 550;
  fill: #1c1919;
}

.vessel-bar-chart-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px 20px;
  padding: 0 20px;
  font-size: 12px;
  background-color: white;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .legend-color {
    width: 14px;
    height: 14px;
    border-radius: 15px;
    background-color: var(--legend-color);
    display: inline-block;
  }

  .legend-label {
    font-weight: 500;
    color: #333;
  }
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 0px;
}

.sticky-bottom-container {
  position: sticky;
  bottom: 0;
  z-index: 10;
  background: white;
  border-top: 1px solid #ddd;
  margin-top: auto;
}

.sticky-x-axis {
  width: 100%;
  overflow: hidden;
  position: relative;

  svg {
    display: block;
  }

  .x-axis {
    transition: transform 0.1s ease;
  }
}

.x-axis {
  .tick line {
    stroke: #ccc;
  }

  .tick text {
    fill: #858383;
    font-size: 12px;
  }
}

.x-axis .domain {
  display: none;
  stroke: none;
}

.chart-content {
  width: var(--chart-width);
}

.no-select {
  user-select: none;
}