import { useMemo } from "react";
import Highcharts from "highcharts";
import { GA_Category, WidgetConstant } from "../types/widget.constant";

// Define the shape of the data items for the chart
interface ChartDataItem {
  label: string;
  value: number;
  color: string;
  url: string;
}

interface UseHighchartsDonutProps {
  data: ChartDataItem[];
  size?: any;
  configKey?: string;
  ga4EventTrigger?: any;
  showLabels: boolean;
}

/**
 * A helper function to generate the tooltip configuration.
 * This keeps the main options object cleaner.
 */
const getTooltipOptions = (showLabels: boolean): Highcharts.TooltipOptions => {
  const baseOptions: Highcharts.TooltipOptions = {
    useHTML: true,
    backgroundColor: "#000000",
    borderColor: "#000000",
    borderRadius: 4,
    shadow: false,
    shape: "square" as Highcharts.TooltipShapeValue,
    className: "custom-sharp-tooltip",
    headerFormat: "",
    // Set hideDelay to 0 for instant disappearance on desktop
    hideDelay: 0,
    // The positioner function is the same for both cases
    positioner: function(labelWidth, labelHeight, point) {
      const chart = this.chart;
      const tooltip = (this as any).label;

      // Add a null/NaN check for point coordinates.
      // If plotX or plotY is invalid, return a safe, hidden position.
      if (
        typeof point.plotX !== "number" ||
        typeof point.plotY !== "number" ||
        isNaN(point.plotX) ||
        isNaN(point.plotY)
      ) {
        return { x: 0, y: -9999 };
      }

      const { plotX, plotY } = point;
      const arrowWidth = 15;

      tooltip.removeClass("arrow-up arrow-down arrow-left arrow-right");

      let x, y;

      if (plotX + arrowWidth + labelWidth > chart.plotWidth) {
        x = plotX - labelWidth - arrowWidth;
      } else {
        x = plotX + arrowWidth;
        tooltip.addClass("arrow-left");
      }

      y = plotY - labelHeight / 2;

      if (y < 0) y = 5;
      if (y + labelHeight > chart.plotHeight)
        y = chart.plotHeight - labelHeight - 5;

      return { x, y };
    },
  };

  // Conditionally add either pointFormatter or pointFormat
  if (showLabels) {
    return {
      ...baseOptions,
      pointFormat:
        '<div style="font-size: 14px; color: #FFFFFF;">' +
        "<div style='line-height: 14px;'>" +
        "{point.name}" +
        "</div>" +
        "<div style='line-height: 14px; margin-top: 5px;'>" +
        '<span style="color:{point.color}; font-size: 14px;">●</span> ' +
        "{point.y}" +
        "</div>" +
        "</div>",
    };
  } else {
    return {
      ...baseOptions,
      pointFormatter: function() {
        const words = this.name.split(" ");
        const firstTwoWords = words.slice(0, 2).join(" ");
        const restOfWords = words.slice(2).join(" ");
        const formattedY =
          '<span style="color:' +
          this.color +
          '; font-size: 14px; display: inline-block;">●</span> ' +
          this.y;

        return (
          '<div style="font-size: 14px; color: #FFFFFF;">' +
          "<div>" +
          firstTwoWords +
          "</div>" +
          "<div>" +
          (restOfWords ? "<span>" + restOfWords + "</span> " : "") +
          formattedY +
          "</div>" +
          "</div>"
        );
      },
    };
  }
};

/**
 * A helper function to generate the plotOptions configuration,
 * including the click event handler for navigation.
 */
const getPlotOptions = (
  size,
  configKey,
  ga4EventTrigger,
  showLabels
): Highcharts.PlotOptions => ({
  pie: {
    innerSize: "65%",
    depth: 22,
    allowPointSelect: false, // Set this to false to disable the "out" animation
    cursor: "pointer",
    size: size,
    states: {
      hover: {
        brightness: 0,
        opacity: 1,
      },
      select: {
        halo: {
          size: 0,
        },
      },
      inactive: {
        opacity: 1,
      },
    },
    point: {
      events: {
        click: function() {
          const point = this as Highcharts.Point & {
            options: { url?: string };
          };

          // Explicitly hide the tooltip on click
          this.series.chart.tooltip.hide();

          if (configKey === WidgetConstant.DEFICIENCIES) {
            ga4EventTrigger(
              GA_Category.DEFICIENCIES,
              point.options.name,
              "Click Pie Chart"
            );
          }
          if (point.options.url) {
            window.open(point.options.url, "_blank");
          }
        },
      },
    },
    dataLabels: {
      enabled: showLabels,
      format: "{point.name}: <b>{point.y}</b>",
      distance: 1,
      style: {
        fontWeight: "normal",
        fontSize: "12px",
        color: "#333",
        textOutline: "none",
        wordBreak: "break-word",
        width: 72,
      },
    },
    showInLegend: false,
  },
});

export const useHighchartsDonut = ({
  data,
  size,
  configKey,
  ga4EventTrigger,
  showLabels,
}: UseHighchartsDonutProps) => {
  const tooltipStyle = `
    .custom-sharp-tooltip { position: relative; padding-top: 60px; }
    .custom-sharp-tooltip::before { content: ""; position: absolute; border: 7px solid transparent; z-index: 1; }
    .arrow-up::before { top: -14px; left: 50%; transform: translateX(-50%); border-bottom-color: #000000; }
    .arrow-down::before { bottom: -14px; left: 50%; transform: translateX(-50%); border-top-color: #000000; }
    .arrow-left::before { left: -14px; top: 50%; transform: translateY(-50%); border-right-color: #000000; }
    .arrow-right::before { right: -14px; top: 50%; transform: translateY(-50%); border-left-color: #000000; }
  `;

  // useMemo ensures the complex options object is only recalculated when data changes.
  const options = useMemo((): Highcharts.Options => {
    const filteredData = data.filter((item) => item.value > 0);
    const chartData = filteredData.map((item) => ({
      name: item.label,
      y: item.value,
      color: item.color,
      url: item.url,
    }));

    return {
      chart: {
        type: "pie",
        backgroundColor: "transparent",
        options3d: { enabled: true, alpha: 45, beta: 0 },
        // Reduce margins to give the chart more space
        margin: [0, 0, 25, 0],
        spacingTop: 0,
        spacingBottom: 0,
        spacingLeft: 0,
        spacingRight: 0,
      },
      title: { text: undefined },
      credits: { enabled: false },
      tooltip: getTooltipOptions(showLabels),
      plotOptions: getPlotOptions(size, configKey, ga4EventTrigger, showLabels),
      legend: {
        layout: "horizontal",
        align: "center",
        verticalAlign: "bottom",
        itemStyle: {
          fontSize: "12px",
          color: "black",
        },
        // Reduce the horizontal padding between items
        itemDistance: 6,
        // Reduce the top and bottom margins for the legend box
        itemMarginTop: 0,
        itemMarginBottom: 0,
      },
      series: [{ type: "pie", name: "Count", data: chartData }],
    };
  }, [data, size, configKey, ga4EventTrigger, showLabels]);

  return { options, tooltipStyle };
};
